"""
Security utilities for CrewAI AWS System
"""

import re
from html import escape
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class SecurityValidator:
    """Security validation and sanitization utilities"""
    
    # Regex patterns for validation
    CONVERSATION_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]{1,50}$')
    SAFE_TEXT_PATTERN = re.compile(r'^[a-zA-Z0-9\s\-_.,!?()]+$')
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """Remove potentially dangerous characters from input"""
        if not isinstance(text, str):
            return str(text)
        
        # Remove potential injection patterns
        text = re.sub(r'[<>"\';\\]', '', text)
        text = escape(text)
        text = text.strip()
        
        # Limit length
        if len(text) > 10000:
            text = text[:10000]
            logger.warning("Input truncated due to excessive length")
        
        return text
    
    @staticmethod
    def validate_conversation_id(conv_id: str) -> bool:
        """Validate conversation ID format"""
        if not conv_id:
            return False
        return bool(SecurityValidator.CONVERSATION_ID_PATTERN.match(conv_id))
    
    @staticmethod
    def validate_crew_type(crew_type: str) -> bool:
        """Validate crew type"""
        valid_types = {
            "cost_analysis", "architecture_design", 
            "optimization_review", "comprehensive_analysis"
        }
        return crew_type in valid_types
    
    @staticmethod
    def sanitize_log_message(message: str) -> str:
        """Sanitize log messages to prevent log injection"""
        if not isinstance(message, str):
            message = str(message)
        
        # Remove newlines and control characters
        message = re.sub(r'[\n\r\t\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', message)
        return message[:1000]  # Limit log message length

class ConfigurationError(Exception):
    """Raised when configuration is invalid"""
    pass

class ValidationError(Exception):
    """Raised when input validation fails"""
    pass
