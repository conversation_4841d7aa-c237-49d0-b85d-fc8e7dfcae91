"""
Intelligent Caching System for CrewAI AWS Manager
"""

import json
import hashlib
from datetime import datetime, timedelta
from typing import Any, Optional, Dict
import logging
from functools import wraps
import asyncio

logger = logging.getLogger(__name__)

class CacheManager:
    """Thread-safe cache manager with TTL support"""
    
    def __init__(self, max_entries: int = 1000, default_ttl_minutes: int = 10):
        self._cache: Dict[str, Any] = {}
        self._cache_ttl: Dict[str, datetime] = {}
        self._access_count: Dict[str, int] = {}
        self.max_entries = max_entries
        self.default_ttl = timedelta(minutes=default_ttl_minutes)
        self._lock = asyncio.Lock()
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Generate a unique cache key from arguments"""
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items())
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get cached result if not expired"""
        async with self._lock:
            if key not in self._cache:
                return None
            
            # Check expiration
            if datetime.now() > self._cache_ttl.get(key, datetime.min):
                await self._remove_key(key)
                return None
            
            # Update access count
            self._access_count[key] = self._access_count.get(key, 0) + 1
            return self._cache[key]
    
    async def set(self, key: str, value: Any, ttl_minutes: Optional[int] = None) -> None:
        """Set cache value with TTL"""
        async with self._lock:
            # Cleanup if cache is full
            if len(self._cache) >= self.max_entries:
                await self._evict_lru()
            
            ttl = timedelta(minutes=ttl_minutes or self.default_ttl.total_seconds() / 60)
            self._cache[key] = value
            self._cache_ttl[key] = datetime.now() + ttl
            self._access_count[key] = 1
    
    async def _remove_key(self, key: str) -> None:
        """Remove key from all cache structures"""
        self._cache.pop(key, None)
        self._cache_ttl.pop(key, None)
        self._access_count.pop(key, None)
    
    async def _evict_lru(self) -> None:
        """Evict least recently used items"""
        if not self._access_count:
            return
        
        # Remove 10% of least accessed items
        items_to_remove = max(1, len(self._cache) // 10)
        sorted_items = sorted(self._access_count.items(), key=lambda x: x[1])
        
        for key, _ in sorted_items[:items_to_remove]:
            await self._remove_key(key)
    
    async def clear(self) -> None:
        """Clear all cached items"""
        async with self._lock:
            self._cache.clear()
            self._cache_ttl.clear()
            self._access_count.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            "total_entries": len(self._cache),
            "max_entries": self.max_entries,
            "hit_ratio": sum(self._access_count.values()) / max(1, len(self._access_count))
        }

# Decorator for caching function results
def cached(ttl_minutes: int = 10, key_func=None):
    """Decorator to cache function results"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            if not hasattr(wrapper, '_cache'):
                wrapper._cache = CacheManager()
            
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = wrapper._cache._generate_key(*args, **kwargs)
            
            # Try to get from cache
            result = await wrapper._cache.get(cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            await wrapper._cache.set(cache_key, result, ttl_minutes)
            return result
        
        return wrapper
    return decorator

# Global cache instance
cache_manager = CacheManager()
