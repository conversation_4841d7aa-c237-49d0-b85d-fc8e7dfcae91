# Core Framework - UPDATED VERSIONS
fastapi>=0.108.0
uvicorn>=0.25.0
streamlit>=1.28.1
requests>=2.31.0
pandas>=2.1.3
plotly>=5.17.0
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.0.0
python-multipart>=0.0.6

# Security & Authentication
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
cryptography>=41.0.0

# AWS Integration
aioboto3>=12.0.0
boto3>=1.34.0
botocore>=1.34.0

# MCP & Multi-Agent - COMPATIBLE VERSIONS
mcp>=1.14.0
crewai>=0.41.1
crewai-tools>=0.4.26
langchain>=0.1.20
langchain-community>=0.0.38
langchain-openai>=0.0.5

# Rate Limiting and Security
slowapi>=0.1.9

# HTTP and Requests
httpx>=0.24.0
aiohttp>=3.8.0

# Data Processing
numpy>=1.24.0
json-repair>=0.7.0

# Caching and Storage
redis>=4.5.0
diskcache>=5.6.0

# Monitoring and Logging
psutil>=5.9.0
structlog>=23.1.0

# Streamlit Extensions
streamlit-chat>=0.1.1

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
mypy>=1.5.0

# CLI and Utilities
click>=8.1.0
rich>=13.0.0
typer>=0.9.0
jsonschema>=4.17.0

# Additional Dependencies
pyyaml>=6.0.1
tiktoken>=0.5.2
anyio>=4.5.0
