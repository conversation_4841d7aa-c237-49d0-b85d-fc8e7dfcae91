"""
CrewAI-Enhanced Streamlit Frontend - PRODUCTION OPTIMIZED VERSION
Multi-agent interface with enhanced UX, caching, and error handling
"""

import streamlit as st
import requests
import json
import uuid
from datetime import datetime, timedelta
import pandas as pd
from typing import Optional, Dict, List, Any
import plotly.express as px
import plotly.graph_objects as go
from urllib.parse import urljoin
import os
import time
import asyncio
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Enhanced Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_TIMEOUT = int(os.getenv("API_TIMEOUT", "120"))
MAX_RETRIES = 3
CACHE_TTL = 300  # 5 minutes

# Enhanced page configuration
st.set_page_config(
    page_title="CrewAI AWS Cost Optimization Assistant",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/your-repo/issues',
        'Report a bug': "https://github.com/your-repo/issues/new",
        'About': "CrewAI-Enhanced AWS Cost Optimization Assistant v4.0"
    }
)

# Enhanced CSS with modern design
st.markdown("""
<style>
    /* Modern dark theme variables */
    :root {
        --primary-color: #1e88e5;
        --secondary-color: #26a69a;
        --success-color: #4caf50;
        --warning-color: #ff9800;
        --error-color: #f44336;
        --background-dark: #0e1117;
        --background-light: #262730;
        --text-light: #fafafa;
        --border-color: #444654;
    }
    
    /* Hide Streamlit default elements */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    
    /* Custom chat container */
    .chat-container {
        background: var(--background-light);
        border-radius: 10px;
        padding: 20px;
        margin: 10px 0;
        border-left: 4px solid var(--primary-color);
    }
    
    /* Message styling */
    .user-message {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 18px 18px 5px 18px;
        margin: 10px 0;
        margin-left: 20%;
    }
    
    .assistant-message {
        background: var(--background-light);
        border: 1px solid var(--border-color);
        padding: 15px;
        border-radius: 18px 18px 18px 5px;
        margin: 10px 0;
        margin-right: 20%;
    }
    
    /* Crew selection styling */
    .crew-card {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        padding: 15px;
        border-radius: 10px;
        margin: 5px 0;
        cursor: pointer;
        transition: transform 0.2s;
    }
    
    .crew-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    }
    
    /* Status indicators */
    .status-success { color: var(--success-color); }
    .status-warning { color: var(--warning-color); }
    .status-error { color: var(--error-color); }
    
    /* Loading animation */
    .loading-dots {
        display: inline-block;
    }
    .loading-dots::after {
        content: '⠋';
        animation: loading 1s infinite;
    }
    @keyframes loading {
        0% { content: '⠋'; }
        12.5% { content: '⠙'; }
        25% { content: '⠹'; }
        37.5% { content: '⠸'; }
        50% { content: '⠼'; }
        62.5% { content: '⠴'; }
        75% { content: '⠦'; }
        87.5% { content: '⠧'; }
        100% { content: '⠇'; }
    }
</style>
""", unsafe_allow_html=True)

# Enhanced API Client with comprehensive error handling
class EnhancedCrewAIAPIClient:
    def __init__(self, base_url: str, timeout: int = 120):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'CrewAI-Streamlit-Client/4.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })

    def _make_request_with_retry(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Enhanced request method with retry logic and better error handling"""
        url = urljoin(self.base_url + '/', endpoint.lstrip('/'))
        
        for attempt in range(MAX_RETRIES):
            try:
                with st.spinner(f"🤖 Connecting to CrewAI (attempt {attempt + 1}/{MAX_RETRIES})..."):
                    response = self.session.request(method, url, timeout=self.timeout, **kwargs)
                    response.raise_for_status()
                    return response
                    
            except requests.exceptions.Timeout:
                if attempt == MAX_RETRIES - 1:
                    st.error(f"⏰ Request timed out after {self.timeout} seconds. Please try again.")
                    st.info("💡 **Tip**: The system might be processing heavy requests. Try again in a moment.")
                    raise
                time.sleep(2 ** attempt)  # Exponential backoff
                
            except requests.exceptions.ConnectionError:
                if attempt == MAX_RETRIES - 1:
                    st.error(f"🔌 Cannot connect to CrewAI API at {self.base_url}")
                    st.info("💡 **Solutions:**\n- Check if the API server is running\n- Verify the API URL in settings\n- Check your internet connection")
                    if st.button("🔄 Retry Connection"):
                        st.rerun()
                    raise
                time.sleep(2 ** attempt)
                
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 503:
                    st.warning("🚧 CrewAI service is initializing. Please wait...")
                    if st.button("🔄 Check Status"):
                        st.rerun()
                elif e.response.status_code == 429:
                    st.warning("🚦 Rate limit reached. Please wait a moment...")
                    time.sleep(5)
                    if attempt == MAX_RETRIES - 1:
                        raise
                else:
                    st.error(f"❌ API Error ({e.response.status_code}): {e.response.text}")
                    if st.button("🔄 Try Again"):
                        st.rerun()
                    raise
                    
            except Exception as e:
                st.error(f"🐛 Unexpected error: {str(e)}")
                if st.button("🔄 Retry"):
                    st.rerun()
                raise
        
        return response

    @st.cache_data(ttl=CACHE_TTL, show_spinner=False)
    def get_system_health(_self) -> Dict:
        """Cached system health check"""
        try:
            response = _self._make_request_with_retry("GET", "/health/detailed")
            return response.json()
        except Exception:
            return {"status": "unhealthy", "error": "Cannot reach API"}

    @st.cache_data(ttl=CACHE_TTL, show_spinner=False) 
    def get_crews_info(_self) -> Dict:
        """Cached crews information"""
        response = _self._make_request_with_retry("GET", "/crews")
        return response.json()

    @st.cache_data(ttl=CACHE_TTL, show_spinner=False)
    def get_agents_info(_self) -> Dict:
        """Cached agents information"""
        response = _self._make_request_with_retry("GET", "/agents")
        return response.json()

    def chat_with_crewai(self, message: str, session_id: str, crew_type: Optional[str] = None) -> Dict:
        """Enhanced chat with progress tracking"""
        payload = {
            "message": message,
            "conversation_id": session_id,
            "crew_type": crew_type,
            "use_tools": True
        }
        
        with st.spinner("🤖 CrewAI agents are working..."):
            # Add progress tracking
            progress_bar = st.progress(0, text="Initializing agents...")
            
            try:
                progress_bar.progress(25, text="Sending request...")
                response = self._make_request_with_retry("POST", "/chat", json=payload)
                
                progress_bar.progress(75, text="Processing response...")
                result = response.json()
                
                progress_bar.progress(100, text="Complete!")
                time.sleep(0.5)  # Brief pause to show completion
                progress_bar.empty()
                
                return result
                
            except Exception as e:
                progress_bar.empty()
                raise e

# Initialize enhanced API client
@st.cache_resource
def get_api_client():
    return EnhancedCrewAIAPIClient(API_BASE_URL, API_TIMEOUT)

# Enhanced session state management
def initialize_session_state():
    """Comprehensive session state initialization"""
    defaults = {
        'session_id': f"crewai_{uuid.uuid4().hex[:12]}",
        'conversation_history': [],
        'selected_crew': None,
        'last_response_time': None,
        'total_messages': 0,
        'user_settings': {
            'show_timestamps': True,
            'show_crew_info': True,
            'auto_scroll': True,
            'theme': 'dark'
        },
        'system_status': None,
        'last_health_check': None
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            st.session_state[key] = value

initialize_session_state()
api_client = get_api_client()

# Enhanced sidebar with comprehensive controls
with st.sidebar:
    st.markdown("### 🚀 **CrewAI Control Center**")
    
    # System health check
    col1, col2 = st.columns(2)
    with col1:
        if st.button("🏥 Health Check", use_container_width=True):
            with st.spinner("Checking system health..."):
                health = api_client.get_system_health()
                st.session_state.system_status = health
                st.session_state.last_health_check = datetime.now()
    
    with col2:
        if st.button("🔄 Refresh Data", use_container_width=True):
            st.cache_data.clear()
            st.success("✅ Cache cleared!")
            time.sleep(1)
            st.rerun()
    
    # Display system status
    if st.session_state.system_status:
        status = st.session_state.system_status
        if status.get('status') == 'healthy':
            st.success("🟢 System Healthy")
        else:
            st.error("🔴 System Issues Detected")
        
        with st.expander("📊 System Details"):
            st.json(status)
    
    st.divider()
    
    # Session management
    st.markdown("### 💬 **Session Management**")
    
    col1, col2 = st.columns(2)
    with col1:
        if st.button("🗑️ Clear Chat", use_container_width=True):
            st.session_state.conversation_history = []
            st.session_state.total_messages = 0
            st.success("✅ Chat cleared!")
            time.sleep(1)
            st.rerun()
    
    with col2:
        if st.button("🔄 New Session", use_container_width=True):
            st.session_state.session_id = f"crewai_{uuid.uuid4().hex[:12]}"
            st.session_state.conversation_history = []
            st.session_state.total_messages = 0
            st.success("✅ New session started!")
            time.sleep(1)
            st.rerun()
    
    # Display session info
    st.info(f"""
    **Session ID**: `{st.session_state.session_id[:8]}...`
    **Messages**: {st.session_state.total_messages}
    **Started**: {datetime.now().strftime('%H:%M')}
    """)
    
    st.divider()
    
    # User settings
    st.markdown("### ⚙️ **Settings**")
    
    st.session_state.user_settings['show_timestamps'] = st.checkbox(
        "Show timestamps", 
        value=st.session_state.user_settings['show_timestamps']
    )
    
    st.session_state.user_settings['show_crew_info'] = st.checkbox(
        "Show crew details", 
        value=st.session_state.user_settings['show_crew_info']
    )
    
    st.session_state.user_settings['auto_scroll'] = st.checkbox(
        "Auto scroll to bottom", 
        value=st.session_state.user_settings['auto_scroll']
    )
    
    st.divider()
    
    # Crew selection
    st.markdown("### 🤖 **Crew Selection**")
    
    try:
        crews_info = api_client.get_crews_info()
        crew_options = ["Auto-detect"] + list(crews_info.keys())
        
        selected_crew = st.selectbox(
            "Choose crew type:",
            crew_options,
            index=0,
            help="Auto-detect analyzes your query to select the best crew"
        )
        
        st.session_state.selected_crew = None if selected_crew == "Auto-detect" else selected_crew
        
        # Display crew information
        if st.session_state.user_settings['show_crew_info'] and selected_crew != "Auto-detect":
            with st.expander(f"ℹ️ {selected_crew} Details"):
                crew_info = crews_info[selected_crew]
                st.write(f"**Agents**: {len(crew_info['agents'])}")
                for agent in crew_info['agents']:
                    st.write(f"• {agent}")
                st.write(f"**Process**: {crew_info['process_type']}")
                
    except Exception as e:
        st.error("❌ Cannot load crew information")
        if st.button("🔄 Retry Loading Crews"):
            st.cache_data.clear()
            st.rerun()

# Main header with enhanced styling
st.markdown("""
<div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; margin-bottom: 20px;">
    <h1 style="color: white; margin: 0;">🤖 CrewAI AWS Cost Optimization Assistant</h1>
    <p style="color: #f0f0f0; margin: 10px 0 0 0;">Multi-agent AI system for specialized AWS consulting and architecture design</p>
    <p style="color: #d0d0d0; font-size: 14px; margin: 5px 0 0 0;">Version 4.0.0 | Production Optimized</p>
</div>
""", unsafe_allow_html=True)

# Enhanced chat interface
st.markdown("### 💬 **Chat Interface**")

# Display conversation history with enhanced styling
if st.session_state.conversation_history:
    chat_container = st.container()
    
    with chat_container:
        for i, turn in enumerate(st.session_state.conversation_history):
            timestamp = turn.get('timestamp', 'Unknown time')
            
            # User message
            st.markdown(f"""
            <div class="user-message">
                <div style="font-size: 12px; opacity: 0.8; margin-bottom: 5px;">
                    👤 You {f"• {timestamp}" if st.session_state.user_settings['show_timestamps'] else ""}
                </div>
                <div>{turn['user_message']}</div>
            </div>
            """, unsafe_allow_html=True)
            
            # Assistant response
            crew_used = turn.get('crew_used', 'Unknown')
            processing_time = turn.get('processing_time_ms', 0)
            
            st.markdown(f"""
            <div class="assistant-message">
                <div style="font-size: 12px; opacity: 0.8; margin-bottom: 10px;">
                    🤖 CrewAI • {crew_used.replace('_', ' ').title()} 
                    {f"• {timestamp}" if st.session_state.user_settings['show_timestamps'] else ""}
                    {f"• {processing_time/1000:.1f}s" if processing_time > 0 else ""}
                </div>
                <div>{turn['assistant_response']}</div>
            </div>
            """, unsafe_allow_html=True)
            
            # Show tools used if available
            tools_used = turn.get('tools_used', [])
            if tools_used and st.session_state.user_settings['show_crew_info']:
                with st.expander(f"🔧 Tools Used ({len(tools_used)})", expanded=False):
                    for tool in tools_used:
                        st.code(f"Tool: {tool.get('tool', 'Unknown')}")
else:
    st.markdown("""
    <div style="text-align: center; padding: 40px; opacity: 0.6;">
        <h3>👋 Welcome to CrewAI!</h3>
        <p>Ask me anything about AWS cost optimization, architecture design, or infrastructure planning.</p>
        <p><strong>Example questions:</strong></p>
        <ul style="text-align: left; display: inline-block;">
            <li>"What's the most cost-effective way to host a web app for 10,000 users?"</li>
            <li>"Design a disaster recovery solution for my e-commerce platform"</li>
            <li>"How can I optimize my current AWS costs by 30%?"</li>
        </ul>
    </div>
    """, unsafe_allow_html=True)

# Enhanced input form
with st.form(key="chat_form", clear_on_submit=True):
    col1, col2 = st.columns([4, 1])
    
    with col1:
        user_input = st.text_area(
            "Ask CrewAI anything about AWS:",
            placeholder="Example: Design a cost-effective architecture for a mobile app backend...",
            height=100,
            help="Be specific about your requirements for better responses"
        )
    
    with col2:
        st.write("")  # Spacing
        submit_button = st.form_submit_button(
            "🚀 Send to CrewAI",
            use_container_width=True,
            type="primary"
        )

# Process user input
if submit_button and user_input.strip():
    try:
        # Validate input
        if len(user_input.strip()) < 10:
            st.warning("⚠️ Please provide a more detailed question (at least 10 characters)")
        elif len(user_input.strip()) > 5000:
            st.warning("⚠️ Question too long. Please keep it under 5000 characters.")
        else:
            # Record start time
            start_time = time.time()
            
            # Send to CrewAI
            response = api_client.chat_with_crewai(
                message=user_input.strip(),
                session_id=st.session_state.session_id,
                crew_type=st.session_state.selected_crew
            )
            
            # Calculate processing time
            processing_time = (time.time() - start_time) * 1000
            
            # Store in conversation history
            conversation_turn = {
                'user_message': user_input.strip(),
                'assistant_response': response['response'],
                'crew_used': response.get('crew_used', 'unknown'),
                'tools_used': response.get('tools_used', []),
                'processing_time_ms': processing_time,
                'timestamp': datetime.now().strftime('%H:%M:%S')
            }
            
            st.session_state.conversation_history.append(conversation_turn)
            st.session_state.total_messages += 1
            st.session_state.last_response_time = processing_time
            
            # Success feedback
            st.success(f"✅ Response generated in {processing_time/1000:.1f}s using {response.get('crew_used', 'unknown').replace('_', ' ').title()}")
            
            # Auto-scroll to bottom if enabled
            if st.session_state.user_settings['auto_scroll']:
                st.rerun()
                
    except Exception as e:
        st.error(f"❌ Failed to get response: {str(e)}")
        
        # Provide specific guidance based on error type
        if "timeout" in str(e).lower():
            st.info("💡 **Tip**: Try asking a simpler question or check your internet connection")
        elif "connection" in str(e).lower():
            st.info("💡 **Tip**: Make sure the CrewAI API server is running")
        else:
            st.info("💡 **Tip**: Try refreshing the page or starting a new session")

# Enhanced footer with statistics
if st.session_state.conversation_history:
    st.divider()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("💬 Messages", st.session_state.total_messages)
    
    with col2:
        if st.session_state.last_response_time:
            st.metric("⚡ Last Response", f"{st.session_state.last_response_time/1000:.1f}s")
    
    with col3:
        crews_used = list(set([turn.get('crew_used', 'unknown') for turn in st.session_state.conversation_history]))
        st.metric("🤖 Crews Used", len(crews_used))
    
    with col4:
        total_tools = sum(len(turn.get('tools_used', [])) for turn in st.session_state.conversation_history)
        st.metric("🔧 Tools Used", total_tools)

# Quick actions for specialized workflows
st.divider()
st.markdown("### ⚡ **Quick Actions**")

col1, col2, col3 = st.columns(3)

with col1:
    if st.button("💰 Cost Analysis Workflow", use_container_width=True):
        st.session_state.selected_crew = "cost_analysis"
        st.info("✅ Switched to Cost Analysis crew. Ask about AWS costs, pricing, or optimization!")

with col2:
    if st.button("🏗️ Architecture Design Workflow", use_container_width=True):
        st.session_state.selected_crew = "architecture_design"
        st.info("✅ Switched to Architecture Design crew. Ask about system design or infrastructure planning!")

with col3:
    if st.button("⚙️ Optimization Review Workflow", use_container_width=True):
        st.session_state.selected_crew = "optimization_review"
        st.info("✅ Switched to Optimization Review crew. Ask about improving existing systems!")

# Debug information (only in development)
if os.getenv("DEBUG", "false").lower() == "true":
    with st.expander("🐛 Debug Information", expanded=False):
        st.json({
            "session_id": st.session_state.session_id,
            "selected_crew": st.session_state.selected_crew,
            "api_base_url": API_BASE_URL,
            "conversation_length": len(st.session_state.conversation_history),
            "user_settings": st.session_state.user_settings
        })
