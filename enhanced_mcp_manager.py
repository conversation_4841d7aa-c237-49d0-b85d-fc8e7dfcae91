"""
Enhanced MCP Manager using AWS credential chain with all optimizations
PRODUCTION-READY VERSION with comprehensive error handling and caching
"""

import logging
import os
import json
import re
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from config import config
from cache_manager import cache_manager, cached
from security import SecurityValidator, ConfigurationError

logger = logging.getLogger(__name__)

class EnhancedMCPMixin:
    """MCP mixin with AWS credential chain authentication and optimizations"""

    def __init__(self):
        self.model_id = config.bedrock_model_id
        self._connection_pool = {}
        self._last_health_check = {}
        
    async def get_async_bedrock_runtime(self):
        """Get async bedrock runtime client using credential chain with connection pooling"""
        try:
            import aioboto3
        except ImportError:
            raise ConfigurationError("aioboto3 is required for async Bedrock operations. Install with: pip install aioboto3")

        # Use connection pooling for AWS sessions
        pool_key = f"{config.aws_profile}_{config.aws_region}"
        
        if pool_key not in self._connection_pool:
            session = aioboto3.Session(
                profile_name=config.aws_profile,
                region_name=config.aws_region
            )
            client = session.client(
                "bedrock-runtime",
                config=aioboto3.session.Config(
                    retries={'max_attempts': 3, 'mode': 'adaptive'},
                    max_pool_connections=50
                )
            )
            self._connection_pool[pool_key] = await client.__aenter__()
        
        return self._connection_pool[pool_key]

    @cached(ttl_minutes=5)
    async def chat_with_bedrock_with_context(
        self, message: str, session_id: str, tools_available: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Enhanced Bedrock chat with AWS credential chain and caching"""
        
        # Sanitize inputs
        message = SecurityValidator.sanitize_input(message)
        if not SecurityValidator.validate_conversation_id(session_id):
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        try:
            # Import session manager with proper error handling
            try:
                from session_manager_new import session_manager
            except ImportError:
                logger.error("Session manager not available")
                return {
                    "response": "Session management not available",
                    "tools_used": [],
                    "session_id": session_id,
                    "error": True
                }

            chat_session = session_manager.get_or_create_session(session_id)
            historical_messages = chat_session.get_bedrock_messages(max_turns=8)
            
            current_messages = historical_messages + [{
                "role": "user", 
                "content": [{"text": message}]
            }]

            system_message = self._build_specialized_system_message(
                chat_session, tools_available, message
            )
            tool_config = self._build_tool_config_for_bedrock(tools_available)

            # Execute with timeout and retry logic
            result = await asyncio.wait_for(
                self._execute_contextual_conversation(
                    messages=current_messages,
                    system_message=system_message,
                    tool_config=tool_config,
                    session_id=session_id,
                    model_id=self.model_id
                ),
                timeout=config.request_timeout
            )

            # Add to session with error handling
            try:
                chat_session.add_turn(
                    user_message=message,
                    assistant_response=result["response"],
                    tools_used=result.get("tools_used", [])
                )
            except Exception as e:
                logger.warning(f"Failed to save session turn: {e}")

            logger.info(f"Completed specialized chat for session {session_id}: {len(result.get('tools_used', []))} tools used")
            
            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "session_id": session_id,
                "cached": False  # This response was not from cache
            }

        except asyncio.TimeoutError:
            logger.error(f"Bedrock chat timed out for session {session_id}")
            return {
                "response": "I apologize, but the request timed out. Please try again with a simpler query.",
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }
        except Exception as e:
            error_msg = SecurityValidator.sanitize_log_message(str(e))
            logger.error(f"Error in specialized chat for session {session_id}: {error_msg}")
            
            # Provide specific error responses
            if "ValidationException" in error_msg:
                if "model" in error_msg.lower():
                    response_text = f"Model validation error. Current model: {self.model_id}. Please check your AWS Bedrock model permissions."
                elif "region" in error_msg.lower():
                    response_text = f"Region validation error. Current region: {config.aws_region}. Please verify region availability."
                else:
                    response_text = f"AWS Bedrock validation error: {error_msg}"
            elif "RetryError" in error_msg:
                response_text = "Connection retry failed. Please check AWS credentials and network connectivity."
            elif "AccessDenied" in error_msg:
                response_text = "Access denied. Please verify your AWS permissions for Bedrock services."
            elif "ThrottlingException" in error_msg:
                response_text = "Request throttled. Please wait a moment and try again."
            else:
                response_text = "I encountered a temporary issue. Please try again."

            return {
                "response": response_text,
                "tools_used": [],
                "session_id": session_id,
                "error": True
            }

    def _build_specialized_system_message(
        self, chat_session, tools_available: Optional[List[str]] = None, user_message: str = ""
    ) -> str:
        """Build specialized system message based on query type with security validation"""
        
        # Sanitize user message
        user_message = SecurityValidator.sanitize_input(user_message)
        
        # Get context safely
        context = ""
        if hasattr(chat_session, "get_context_for_bedrock") and callable(chat_session.get_context_for_bedrock):
            try:
                context = chat_session.get_context_for_bedrock()
                context = SecurityValidator.sanitize_input(context)
            except Exception as e:
                logger.warning(f"Failed to get session context: {e}")
                context = ""

        message_lower = user_message.lower()
        
        # Detect query type with improved patterns
        is_service_addition = any(keyword in message_lower for keyword in [
            "add service", "adding", "cost impact", "incremental cost", 
            "current", "existing", "integrate", "include"
        ])

        is_architecture_design = any(keyword in message_lower for keyword in [
            "design", "architecture", "from scratch", "deploy", "solution", 
            "complete", "build", "create", "plan", "structure"
        ])

        base_instructions = f"""You are an expert AWS Cloud Solutions Architect with access to comprehensive AWS MCP servers.

**Current Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} IST
**AWS Region:** {config.aws_region}
**Model:** {self.model_id}

**Core Capabilities:**
1. **Service Addition Cost Analysis**: Calculate incremental costs when adding new services to existing infrastructure
2. **Complete Solution Design**: Generate full architecture, diagrams, costs, and system requirements for new deployments
3. **Cost Optimization**: Identify savings opportunities and right-sizing recommendations
4. **Security & Compliance**: Ensure solutions meet enterprise security standards

**Always Use Tools Iteratively:**
- Use multiple tools in sequence to gather complete information
- Continue using tools until you have comprehensive data for your analysis
- Validate information from multiple sources when possible
- Only stop when you can provide a complete, well-supported answer

**Always Include:**
- Detailed cost breakdowns (monthly/annual projections)
- Optimization recommendations (Reserved Instances, Spot, right-sizing)
- Architecture trade-offs and alternatives
- Security and compliance considerations
- Monitoring and operational overhead costs
- Implementation timelines and risk assessments"""

        if is_service_addition:
            specialized_instructions = """

**SERVICE ADDITION COST ANALYSIS MODE:**
For service addition requests, follow this systematic workflow:

1. **Get Current Baseline** (use cost-explorer tools):
   - Retrieve current monthly costs by service and resource
   - Identify existing infrastructure patterns and usage
   - Analyze historical cost trends and growth patterns
   - Document current architecture dependencies

2. **Calculate Incremental Costs** (use aws-pricing tools):
   - Get detailed pricing for the new service in the specified region
   - Calculate costs based on expected usage patterns
   - Include data transfer costs between services
   - Factor in integration and setup costs

3. **Optimization Analysis** (use billing-cost-management tools):
   - Identify right-sizing opportunities for existing resources
   - Recommend Reserved Instances or Savings Plans where applicable
   - Suggest architectural optimizations to reduce overall costs
   - Analyze potential volume discounts

4. **Comprehensive Impact Assessment:**
   - Incremental monthly/annual costs with confidence intervals
   - Total cost impact (percentage increase) on current spend
   - Alternative service options comparison matrix
   - ROI analysis and break-even timeline
   - Implementation timeline with cost phases
   - Risk assessment and mitigation strategies"""

        elif is_architecture_design:
            specialized_instructions = """

**COMPLETE SOLUTION DESIGN MODE:**
For architecture design requests, follow this comprehensive workflow:

1. **Requirements Analysis & Architecture Design**:
   - Analyze functional and non-functional requirements
   - Design Well-Architected solution following AWS best practices
   - Generate infrastructure templates using aws-cdk tools
   - Define security, scalability, and availability patterns
   - Consider multi-region deployment strategies

2. **Visual Documentation** (use aws-diagram tools):
   - Create professional architecture diagrams
   - Show detailed component relationships and data flow
   - Include network topology and security boundaries
   - Document API integrations and dependencies

3. **Comprehensive Cost Analysis** (use aws-pricing tools):
   - Estimate all component costs (compute, storage, networking)
   - Include operational costs (monitoring, backups, support)
   - Provide scaling cost projections with usage tiers
   - Compare different architectural alternatives

4. **Operational Requirements** (use aws-cloudwatch tools):
   - Define monitoring and alerting strategy
   - Set performance and availability targets
   - Design disaster recovery and backup requirements
   - Plan capacity management and auto-scaling policies

5. **Complete Deliverable Package:**
   - Architecture diagrams with detailed component specifications
   - Comprehensive cost breakdown and projections
   - Implementation roadmap with milestones and timelines
   - Operational runbook and best practices guide
   - Security checklist and compliance validation"""

        else:
            specialized_instructions = """

**GENERAL AWS CONSULTING MODE:**
- Analyze the query to determine if it requires service addition analysis or architecture design
- Use appropriate tools to provide comprehensive AWS guidance
- Focus on cost optimization and Well-Architected principles
- Provide actionable recommendations with clear next steps"""

        # Build tool availability hint
        tool_hint = ""
        if tools_available:
            available_servers = set()
            for tool in tools_available:
                if "::" in tool:
                    server_name = tool.split("::")[0]
                    available_servers.add(server_name)
            
            if available_servers:
                tool_hint = f"\n\n**Available AWS MCP Servers:** {', '.join(sorted(available_servers))}"
                tool_hint += "\n**Important:** Use these tools iteratively to gather complete information before providing your final answer."

        session_context = f"\n\n**Session Context:**\n{context}" if context else ""

        return base_instructions + specialized_instructions + session_context + tool_hint

    def _build_tool_config_for_bedrock(self, tools_available: Optional[List[str]] = None) -> Dict[str, Any]:
        """Build tool configuration for Bedrock with validation"""
        if not tools_available:
            return {"tools": []}
        
        # Validate and sanitize tool names
        validated_tools = []
        for tool in tools_available:
            tool = SecurityValidator.sanitize_input(tool)
            if "::" in tool and len(tool.split("::")) == 2:
                validated_tools.append(tool)
        
        return {
            "tools": validated_tools,
            "toolConfig": {
                "toolUseCallback": True,
                "maxIterations": config.max_iterations
            }
        }

    async def _execute_contextual_conversation(
        self, messages: List[Dict], system_message: str, tool_config: Dict,
        session_id: str, model_id: str
    ) -> Dict[str, Any]:
        """Execute conversation with comprehensive error handling and retries"""
        
        max_retries = 3
        base_delay = 1
        
        for attempt in range(max_retries):
            try:
                # Get Bedrock client
                bedrock_client = await self.get_async_bedrock_runtime()
                
                # Prepare request with size limits
                request_data = {
                    "modelId": model_id,
                    "messages": messages[-10:],  # Limit message history
                    "system": [{"text": system_message[:4000]}],  # Limit system message
                    "inferenceConfig": {
                        "temperature": 0.1,
                        "maxTokens": 4000,
                        "stopSequences": ["Human:", "Assistant:"]
                    }
                }
                
                # Add tools if available
                if tool_config.get("tools"):
                    request_data["toolConfig"] = tool_config
                
                # Execute with proper error handling
                response = await bedrock_client.converse(**request_data)
                
                # Process response safely
                if response and "output" in response:
                    content = response["output"].get("message", {}).get("content", [])
                    if content:
                        response_text = ""
                        tools_used = []
                        
                        for item in content:
                            if "text" in item:
                                response_text += item["text"]
                            elif "toolUse" in item:
                                tools_used.append({
                                    "tool": item["toolUse"].get("name"),
                                    "input": item["toolUse"].get("input", {})
                                })
                        
                        return {
                            "response": response_text or "I apologize, but I couldn't generate a response.",
                            "tools_used": tools_used,
                            "usage": response.get("usage", {}),
                            "model_id": model_id
                        }
                
                return {
                    "response": "I apologize, but I received an empty response. Please try rephrasing your question.",
                    "tools_used": [],
                    "usage": {},
                    "model_id": model_id
                }
                
            except Exception as e:
                error_msg = SecurityValidator.sanitize_log_message(str(e))
                logger.warning(f"Bedrock conversation attempt {attempt + 1} failed: {error_msg}")
                
                if attempt == max_retries - 1:
                    # Last attempt failed
                    return {
                        "response": f"I encountered technical difficulties. Please try again later. Error: {error_msg}",
                        "tools_used": [],
                        "usage": {},
                        "error": True
                    }
                
                # Exponential backoff
                await asyncio.sleep(base_delay * (2 ** attempt))

    async def cleanup(self):
        """Clean up connection pool and resources"""
        for client in self._connection_pool.values():
            try:
                await client.__aexit__(None, None, None)
            except Exception as e:
                logger.warning(f"Error closing Bedrock client: {e}")
        
        self._connection_pool.clear()
        logger.info("Enhanced MCP Manager cleaned up")

# Global instance
enhanced_mcp_manager = EnhancedMCPMixin()
