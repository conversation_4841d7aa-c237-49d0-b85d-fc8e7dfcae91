"""
CrewAI-Enhanced AWS Cost Optimization and Architecture Design System
Multi-agent orchestration using AWS credential chain (profiles)
OPTIMIZED VERSION with all fixes applied
"""

import logging
import os
import json
import asyncio
import concurrent.futures
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment first
load_dotenv()

# Import config and set OpenAI key before CrewAI imports
from config import config
from cache_manager import cache_manager, cached
from security import SecurityValidator, ConfigurationError

# Set OpenAI API key for CrewAI (required even if memory is disabled)
if config.openai_api_key:
    os.environ["OPENAI_API_KEY"] = config.openai_api_key
    logger = logging.getLogger(__name__)
    logger.info("OpenAI API key configured for CrewAI")
else:
    # Set a placeholder key to prevent CrewAI validation errors
    os.environ["OPENAI_API_KEY"] = "sk-1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
    logger = logging.getLogger(__name__)
    logger.warning("Using placeholder OpenAI API key - some features may not work")

# Now import CrewAI after setting the API key
from crewai import Agent, Crew, Process, Task, LLM
from crewai.tools import BaseTool

logger = logging.getLogger(__name__)

class EnhancedCircuitBreaker:
    """Enhanced circuit breaker with error type categorization"""
    def __init__(self, failure_threshold=5, timeout=600):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"

    def record_failure(self, error_type="general"):
        """Record failure with categorization"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()

        # Different thresholds for different error types
        if error_type == "token_limit":
            threshold = 2  # Faster circuit breaking for token issues
        else:
            threshold = self.failure_threshold

        if self.failure_count >= threshold:
            self.state = "OPEN"
            logger.warning(f"Circuit breaker OPEN due to {error_type} errors")

    def can_execute(self):
        """Check if execution is allowed"""
        if self.state == "CLOSED":
            return True

        if self.state == "OPEN":
            if self.last_failure_time and datetime.now() - self.last_failure_time > timedelta(seconds=self.timeout):
                self.state = "HALF_OPEN"
                return True
            return False

        return True  # HALF_OPEN state

    def record_success(self):
        """Record successful execution"""
        if self.state == "HALF_OPEN":
            self.state = "CLOSED"
            self.failure_count = 0
            logger.info("Circuit breaker CLOSED - system recovered")

# Circuit Breaker Implementation
class CircuitBreaker:
    """Circuit breaker for handling model failures"""
    
    def __init__(self, failure_threshold: int = 3, timeout: int = 300):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func, *args, **kwargs):
        if self.state == "OPEN":
            if (datetime.now() - self.last_failure_time).total_seconds() > self.timeout:
                self.state = "HALF_OPEN"
            else:
                raise Exception("Circuit breaker is OPEN - service temporarily unavailable")
        
        try:
            result = func(*args, **kwargs)
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
            return result
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = datetime.now()
            
            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
            raise e

# Enhanced MCP Base Tool with proper async handling
class MCPBaseTool(BaseTool):
    """Base class for MCP-integrated tools with optimized async handling"""

    def __init__(self, name: str, description: str, mcp_server: str, mcp_tool: str):
        super().__init__(name=name, description=description)
        self._mcp_server = mcp_server
        self._mcp_tool = mcp_tool

    async def _call_mcp_tool_async(self, **kwargs):
        """Async call to MCP tool"""
        try:
            # Import here to avoid circular imports
            from main_enhanced import mcp_manager
            
            if not mcp_manager:
                return f"MCP manager not available. Using fallback response for {self.name}."

            connection = mcp_manager.connections.get(self._mcp_server)
            if not connection or connection.status != "connected":
                return f"MCP server {self._mcp_server} not connected. Using fallback response."

            # Call the MCP tool with timeout
            result = await asyncio.wait_for(
                connection.session.call_tool(self._mcp_tool, kwargs),
                timeout=config.max_tool_execution_time
            )
            return str(result.content[0].text) if result.content else "No response from MCP tool"

        except asyncio.TimeoutError:
            logger.error(f"MCP tool call timed out: {self._mcp_server}::{self._mcp_tool}")
            return f"Tool execution timed out for {self.name}"
        except Exception as e:
            logger.error(f"MCP tool call failed for {self._mcp_server}::{self._mcp_tool}: {e}")
            return f"Error calling MCP tool: {e}"

    def _run(self, **kwargs) -> str:
        """Synchronous wrapper with proper async handling"""
        try:
            # Check if we're in an event loop
            try:
                loop = asyncio.get_running_loop()
                # Create a task and get result
                task = loop.create_task(self._call_mcp_tool_async(**kwargs))
                # Use a timeout to prevent hanging
                return asyncio.run_coroutine_threadsafe(task, loop).result(
                    timeout=config.max_tool_execution_time + 5
                )
            except RuntimeError:
                # No event loop running
                return asyncio.run(self._call_mcp_tool_async(**kwargs))
        except Exception as e:
            return f"Tool execution failed: {e}"

# Optimized tool classes
class GetCostAndUsageTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="get_cost_and_usage",
            description="Retrieve real AWS cost and usage data for analysis using Cost Explorer",
            mcp_server="cost-explorer",
            mcp_tool="get_cost_and_usage"
        )

class GetAWSPricingTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="get_aws_pricing",
            description="Get real-time AWS service pricing information",
            mcp_server="aws-pricing",
            mcp_tool="get_pricing"
        )

class AnalyzeCostTrendsTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="analyze_cost_trends",
            description="Analyze real historical cost trends and patterns from AWS Cost Explorer",
            mcp_server="cost-explorer",
            mcp_tool="get_cost_trends"
        )

class GenerateCloudFormationTemplateTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="generate_cloudformation_template",
            description="Generate real CloudFormation templates for AWS resources",
            mcp_server="cloudformation",
            mcp_tool="create_template"
        )

class ValidateArchitectureTool(BaseTool):
    name: str = "validate_architecture"
    description: str = "Validate architecture against AWS Well-Architected principles"

    @cached(ttl_minutes=5)
    def _run(self, architecture: str) -> str:
        # Sanitize input
        architecture = SecurityValidator.sanitize_input(architecture)
        return f"Architecture validation for: {architecture}. Score: 85/100. Recommendations: Add Auto Scaling, implement backup strategy."

class EstimateSolutionCostTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="estimate_solution_cost",
            description="Estimate real costs for proposed AWS solution using AWS Pricing API",
            mcp_server="aws-pricing",
            mcp_tool="calculate_cost"
        )

class IdentifyCostSavingsTool(MCPBaseTool):
    def __init__(self):
        super().__init__(
            name="identify_cost_savings",
            description="Identify real cost savings opportunities using AWS Cost Explorer and Billing data",
            mcp_server="billing-cost-management",
            mcp_tool="get_savings_opportunities"
        )

class RecommendInstanceTypesTool(BaseTool):
    name: str = "recommend_instance_types"
    description: str = "Recommend optimal EC2 instance types based on workload requirements"

    @cached(ttl_minutes=15)
    def _run(self, workload: str) -> str:
        workload = SecurityValidator.sanitize_input(workload)
        return f"Instance recommendations for {workload}: c5.large for compute-intensive, r5.xlarge for memory-intensive workloads."

# Main CrewAI Manager with all optimizations
class AWSCrewManager:
    """CrewAI-based manager with comprehensive optimizations"""

    def __init__(self):
        self.model_id = config.bedrock_model_id
        self._recent_model_errors = []
        self.circuit_breaker = EnhancedCircuitBreaker(
            failure_threshold=config.circuit_breaker_threshold,
            timeout=config.circuit_breaker_timeout
        )
        
        # Initialize LLM with fallbacks
        self.llm = self._initialize_bedrock_llm_with_fallbacks()
        
        # Initialize tools and agents
        self._aws_tools = self._initialize_aws_tools()
        self.agents = self._create_aws_agents()
        self.crews = self._create_specialized_crews()
        
        logger.info("✅ CrewAI AWS Manager initialized with all optimizations")

    def _initialize_bedrock_llm_with_fallbacks(self) -> LLM:
        """Initialize Bedrock LLM with multiple model fallbacks"""
        models_to_try = [self.model_id] + config.bedrock_fallback_models
        
        for model_id in models_to_try:
            try:
                return self.circuit_breaker.call(self._try_initialize_bedrock_model, model_id)
            except Exception as e:
                logger.warning(f"Failed to initialize {model_id}: {e}")
                continue
        
        # If all Bedrock models fail, raise an error
        raise ConfigurationError(
            "All AWS Bedrock models failed to initialize. "
            "Please check your AWS credentials, region, and model permissions."
        )

    def _try_initialize_bedrock_model(self, model_id: str) -> LLM:
        """Try to initialize a specific Bedrock model"""
        import boto3
        
        # Use AWS credential chain
        session = boto3.Session(
            profile_name=config.aws_profile,
            region_name=config.aws_region
        )
        
        # Test credentials
        sts = session.client('sts')
        identity = sts.get_caller_identity()
        logger.info(f"✅ AWS Identity verified: {identity.get('Arn', 'Unknown')}")

        # Initialize CrewAI LLM with safe token limits
        llm = LLM(
            model=f"bedrock/{model_id}",
            aws_region_name=config.aws_region,
            temperature=0.1,
            max_tokens=8000  # Set below the 10000 limit for safety
        )
        
        logger.info(f"✅ Successfully initialized Bedrock model: {model_id}")
        return llm

    def _initialize_aws_tools(self) -> List[BaseTool]:
        """Initialize AWS tools for CrewAI agents"""
        return [
            GetCostAndUsageTool(),
            GetAWSPricingTool(),
            AnalyzeCostTrendsTool(),
            GenerateCloudFormationTemplateTool(),
            ValidateArchitectureTool(),
            EstimateSolutionCostTool(),
            IdentifyCostSavingsTool(),
            RecommendInstanceTypesTool()
        ]

    def _create_aws_agents(self) -> Dict[str, Agent]:
        """Create specialized AWS agents with optimized configurations"""
        agents = {
            'cost_analyst': Agent(
                role='Senior AWS Cost Analyst',
                goal='Analyze AWS costs, identify optimization opportunities, and provide detailed financial insights',
                backstory="""You are a seasoned AWS cost optimization expert with deep knowledge of AWS billing and cost structures, Reserved Instances and Savings Plans, cost allocation and tagging strategies, FinOps best practices, and multi-account cost management. You excel at finding hidden costs and providing actionable optimization recommendations.""",
                llm=self.llm,
                tools=self._get_tools_for_agent('cost_analyst'),
                verbose=True,
                memory=config.enable_memory,
                max_iter=config.max_iterations,
                allow_delegation=True
            ),
            'solutions_architect': Agent(
                role='AWS Solutions Architect',
                goal='Design optimal, scalable, and cost-effective AWS architectures',
                backstory="""You are an expert AWS Solutions Architect with extensive experience in AWS Well-Architected Framework, multi-tier application design, microservices and serverless architectures, security and compliance requirements, and high availability and disaster recovery. You create comprehensive solutions that balance performance, cost, and scalability.""",
                llm=self.llm,
                tools=self._get_tools_for_agent('solutions_architect'),
                verbose=True,
                memory=config.enable_memory,
                max_iter=config.max_iterations,
                allow_delegation=True
            ),
            'optimization_specialist': Agent(
                role='AWS Optimization Specialist',
                goal='Provide specific optimization recommendations and implementation guidance',
                backstory="""You are a hands-on AWS optimization specialist focused on right-sizing recommendations, performance optimization, cost-performance trade-offs, automation and monitoring setup, and implementation best practices. You provide practical, actionable advice for immediate improvements.""",
                llm=self.llm,
                tools=self._get_tools_for_agent('optimization_specialist'),
                verbose=True,
                memory=config.enable_memory,
                max_iter=3,
                allow_delegation=False
            ),
            'compliance_advisor': Agent(
                role='AWS Compliance and Security Advisor',
                goal='Ensure solutions meet security and compliance requirements while optimizing costs',
                backstory="""You are a security-focused AWS expert specializing in AWS security best practices, compliance frameworks (SOC 2, PCI DSS, HIPAA, GDPR), cost-effective security implementations, risk assessment and mitigation, and governance and policy enforcement. You ensure solutions are both secure and cost-optimized.""",
                llm=self.llm,
                tools=self._get_tools_for_agent('compliance_advisor'),
                verbose=True,
                memory=config.enable_memory,
                max_iter=3,
                allow_delegation=False
            )
        }
        return agents

    def _get_tools_for_agent(self, agent_type: str) -> List[BaseTool]:
        """Get specific tools for each agent type with MCP integration"""
        # First try to get MCP tools, fallback to local tools
        mcp_tools = self._get_mcp_tools_for_agent(agent_type)
        if mcp_tools:
            logger.info(f"Using {len(mcp_tools)} MCP tools for {agent_type}")
            return mcp_tools

        # Fallback to local tools
        tool_mapping = {
            'cost_analyst': ['get_cost_and_usage', 'get_aws_pricing', 'analyze_cost_trends', 'identify_cost_savings'],
            'solutions_architect': ['generate_cloudformation_template', 'validate_architecture', 'estimate_solution_cost'],
            'optimization_specialist': ['recommend_instance_types', 'identify_cost_savings', 'get_aws_pricing'],
            'compliance_advisor': ['validate_architecture', 'estimate_solution_cost']
        }

        agent_tool_names = tool_mapping.get(agent_type, [])
        local_tools = [tool for tool in self._aws_tools if tool.name in agent_tool_names]
        logger.info(f"Using {len(local_tools)} local tools for {agent_type} (MCP not available)")
        return local_tools

    def _get_mcp_tools_for_agent(self, agent_type: str) -> List[BaseTool]:
        """Get MCP tools for specific agent type"""
        try:
            from main_enhanced import mcp_manager
            if not mcp_manager:
                return []

            # Define which MCP tools each agent should use
            mcp_tool_mapping = {
                'cost_analyst': [
                    ('cost-explorer', 'get_cost_and_usage'),
                    ('cost-explorer', 'get_cost_trends'),
                    ('aws-pricing', 'get_pricing'),
                    ('billing-cost-management', 'get_savings_opportunities')
                ],
                'solutions_architect': [
                    ('cloudformation', 'create_template'),
                    ('aws-pricing', 'calculate_cost'),
                    ('aws-diagram', 'create_architecture_diagram')
                ],
                'optimization_specialist': [
                    ('billing-cost-management', 'get_savings_opportunities'),
                    ('aws-pricing', 'get_pricing'),
                    ('cost-explorer', 'get_rightsizing_recommendations')
                ],
                'compliance_advisor': [
                    ('cloudformation', 'validate_template'),
                    ('aws-pricing', 'calculate_cost')
                ]
            }

            agent_mcp_tools = mcp_tool_mapping.get(agent_type, [])
            available_tools = []

            for server_name, tool_name in agent_mcp_tools:
                connection = mcp_manager.connections.get(server_name)
                if connection and connection.status == "connected":
                    mcp_tool = self._create_mcp_tool_wrapper(server_name, tool_name, connection)
                    if mcp_tool:
                        available_tools.append(mcp_tool)

            return available_tools

        except Exception as e:
            logger.error(f"Error getting MCP tools for {agent_type}: {e}")
            return []

    def _create_mcp_tool_wrapper(self, server_name: str, tool_name: str, connection) -> Optional[BaseTool]:
        """Create a CrewAI-compatible wrapper for MCP tools"""
        try:
            mcp_tool_info = connection.tools.get(tool_name)
            if not mcp_tool_info:
                return None

            class MCPToolWrapper(BaseTool):
                name: str = f"{server_name}_{tool_name}"
                description: str = mcp_tool_info.description or f"MCP tool {tool_name} from {server_name}"

                def __init__(self, server_name: str, tool_name: str, connection):
                    super().__init__()
                    self._server_name = server_name
                    self._tool_name = tool_name
                    self._connection = connection

                def _run(self, **kwargs) -> str:
                    try:
                        # Sanitize inputs
                        sanitized_kwargs = {
                            k: SecurityValidator.sanitize_input(str(v)) 
                            for k, v in kwargs.items()
                        }
                        
                        # Use the optimized async pattern
                        return asyncio.run(self._async_run(**sanitized_kwargs))
                    except Exception as e:
                        return f"Error calling MCP tool {self._tool_name}: {e}"

                async def _async_run(self, **kwargs) -> str:
                    try:
                        result = await asyncio.wait_for(
                            self._connection.session.call_tool(self._tool_name, kwargs),
                            timeout=config.max_tool_execution_time
                        )
                        if result.content:
                            return str(result.content[0].text)
                        return "No response from MCP tool"
                    except asyncio.TimeoutError:
                        return f"MCP tool {self._tool_name} timed out"
                    except Exception as e:
                        return f"MCP tool error: {e}"

            return MCPToolWrapper(server_name, tool_name, connection)

        except Exception as e:
            logger.error(f"Error creating MCP tool wrapper for {server_name}::{tool_name}: {e}")
            return None

    def _create_specialized_crews(self) -> Dict[str, Crew]:
        """Create specialized crews for different AWS consulting scenarios"""
        return {
            'cost_analysis': Crew(
                agents=[self.agents['cost_analyst'], self.agents['optimization_specialist']],
                tasks=[],
                process=Process.sequential,
                memory=config.enable_memory,
                verbose=True,
                max_rpm=30
            ),
            'architecture_design': Crew(
                agents=[self.agents['solutions_architect'], self.agents['cost_analyst'], self.agents['compliance_advisor']],
                tasks=[],
                process=Process.hierarchical,
                manager_llm=self.llm,
                memory=config.enable_memory,
                verbose=True,
                max_rpm=30
            ),
            'optimization_review': Crew(
                agents=[self.agents['optimization_specialist'], self.agents['cost_analyst']],
                tasks=[],
                process=Process.sequential,
                memory=config.enable_memory,
                verbose=True,
                max_rpm=30
            ),
            'comprehensive_analysis': Crew(
                agents=list(self.agents.values()),
                tasks=[],
                process=Process.hierarchical,
                manager_llm=self.llm,
                memory=config.enable_memory,
                verbose=True,
                max_rpm=30
            )
        }

    async def chat_with_crewai_context(self, message: str, session_id: str, crew_type: Optional[str] = None) -> Dict[str, Any]:
        """Enhanced chat using CrewAI multi-agent system with all optimizations"""
        try:
            # Sanitize inputs
            message = SecurityValidator.sanitize_input(message)
            if not SecurityValidator.validate_conversation_id(session_id):
                session_id = f"crew_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Import session manager here to avoid circular imports
            from session_manager_new import session_manager
            chat_session = session_manager.get_or_create_session(session_id)

            # Detect crew type if not provided
            if not crew_type:
                crew_type = self._detect_crew_type(message)
            elif not SecurityValidator.validate_crew_type(crew_type):
                crew_type = "comprehensive_analysis"

            # Create tasks and execute
            tasks = self._create_tasks_for_query(message, crew_type)
            crew = self.crews[crew_type]
            crew.tasks = tasks

            # Execute with circuit breaker protection
            try:
                result = self.circuit_breaker.call(self._execute_crew_with_recovery, crew, message, session_id)
            except Exception as e:
                logger.error(f"Circuit breaker activated: {e}")
                result = await self._execute_single_agent_fallback(message, crew_type)

            # Add to session (async call)
            try:
                import asyncio
                if asyncio.iscoroutinefunction(chat_session.add_turn):
                    await chat_session.add_turn(
                        user_message=message,
                        assistant_response=result["response"],
                        tools_used=result.get("tools_used", [])
                    )
                else:
                    # Fallback for sync version
                    chat_session.add_turn(
                        user_message=message,
                        assistant_response=result["response"],
                        tools_used=result.get("tools_used", [])
                    )
            except Exception as e:
                logger.warning(f"Failed to add session turn: {e}")

            return {
                "response": result["response"],
                "tools_used": result.get("tools_used", []),
                "crew_used": crew_type,
                "agents_involved": [agent.role for agent in crew.agents],
                "session_id": session_id,
                "circuit_breaker_state": self.circuit_breaker.state
            }

        except Exception as e:
            error_msg = SecurityValidator.sanitize_log_message(str(e))
            logger.error(f"CrewAI execution error for session {session_id}: {error_msg}")
            
            return {
                "response": f"I encountered an issue but can provide AWS guidance. {self._get_fallback_response(message)}",
                "tools_used": [],
                "crew_used": "fallback",
                "session_id": session_id,
                "error": True
            }

    def _detect_crew_type(self, message: str) -> str:
        """Detect which crew should handle the query"""
        message_lower = message.lower()
        
        if any(keyword in message_lower for keyword in ["cost", "pricing", "bill", "expense", "budget", "saving", "optimize cost"]):
            return "cost_analysis"
        elif any(keyword in message_lower for keyword in ["design", "architecture", "solution", "deploy", "build", "create"]):
            return "architecture_design"
        elif any(keyword in message_lower for keyword in ["optimize", "improve", "performance", "right-size", "efficiency"]):
            return "optimization_review"
        else:
            return "comprehensive_analysis"

    def _create_tasks_for_query(self, message: str, crew_type: str) -> List[Task]:
        """Create dynamic tasks based on query and crew type"""
        tasks = []
        
        if crew_type == "cost_analysis":
            tasks = [
                Task(
                    description=f"Analyze the following AWS cost question: {message}. Provide detailed cost breakdown and analysis.",
                    expected_output="Comprehensive cost analysis with specific recommendations and pricing details",
                    agent=self.agents['cost_analyst']
                ),
                Task(
                    description="Based on the cost analysis, provide specific optimization recommendations with implementation steps.",
                    expected_output="Actionable optimization recommendations with expected savings and implementation timeline",
                    agent=self.agents['optimization_specialist']
                )
            ]
        elif crew_type == "architecture_design":
            tasks = [
                Task(
                    description=f"Design an AWS solution for: {message}. Create a comprehensive architecture following AWS Well-Architected principles.",
                    expected_output="Complete architecture design with component details, data flow, and scalability considerations",
                    agent=self.agents['solutions_architect']
                ),
                Task(
                    description="Calculate detailed cost estimates for the proposed architecture including setup and operational costs.",
                    expected_output="Detailed cost breakdown with monthly/annual projections and cost optimization opportunities",
                    agent=self.agents['cost_analyst']
                ),
                Task(
                    description="Review the architecture for security and compliance requirements, ensuring cost-effective implementation.",
                    expected_output="Security and compliance assessment with recommendations for cost-effective implementation",
                    agent=self.agents['compliance_advisor']
                )
            ]
        elif crew_type == "optimization_review":
            tasks = [
                Task(
                    description=f"Review and optimize the following AWS setup: {message}. Identify specific areas for improvement.",
                    expected_output="Detailed optimization report with specific recommendations and expected impact",
                    agent=self.agents['optimization_specialist']
                ),
                Task(
                    description="Quantify the potential cost savings and provide implementation roadmap for the optimization recommendations.",
                    expected_output="Cost savings analysis with prioritized implementation plan and ROI calculations",
                    agent=self.agents['cost_analyst']
                )
            ]
        else:
            tasks = [
                Task(
                    description=f"Provide comprehensive AWS consulting for: {message}. Analyze all aspects including cost, architecture, and optimization.",
                    expected_output="Complete AWS consulting report covering all relevant aspects with actionable recommendations",
                    agent=self.agents['solutions_architect']
                )
            ]
        
        return tasks

    def _execute_crew_with_recovery(self, crew: Crew, message: str, session_id: str) -> Dict[str, Any]:
        """Execute crew with error recovery mechanisms"""
        try:
            result = crew.kickoff()
            tools_used = []
            
            for agent in crew.agents:
                if hasattr(agent, '_tools_used'):
                    tools_used.extend(agent._tools_used)
            
            return {
                "response": str(result),
                "tools_used": tools_used,
                "success": True
            }
        except Exception as e:
            logger.error(f"Crew execution failed: {e}")
            return asyncio.run(self._execute_single_agent_fallback(message, "cost_analysis"))

    async def _execute_single_agent_fallback(self, message: str, crew_type: str) -> Dict[str, Any]:
        """Fallback to single agent execution when crew fails"""
        try:
            agent_mapping = {
                "cost_analysis": self.agents['cost_analyst'],
                "architecture_design": self.agents['solutions_architect'],
                "optimization_review": self.agents['optimization_specialist'],
                "comprehensive_analysis": self.agents['solutions_architect']
            }

            agent = agent_mapping.get(crew_type, self.agents['cost_analyst'])
            task = Task(
                description=f"Provide AWS consulting for: {message}",
                expected_output="Comprehensive response with actionable recommendations",
                agent=agent
            )
            
            single_crew = Crew(
                agents=[agent],
                tasks=[task],
                process=Process.sequential,
                verbose=False
            )
            
            result = single_crew.kickoff()
            return {
                "response": str(result),
                "tools_used": [],
                "success": True,
                "fallback_mode": True
            }
        except Exception as e:
            logger.error(f"Single agent fallback failed: {e}")
            return {
                "response": self._get_fallback_response(message),
                "tools_used": [],
                "success": False,
                "fallback_mode": True
            }

    def _get_fallback_response(self, message: str) -> str:
        """Generate fallback response when all agents fail"""
        message_lower = message.lower()
        
        if "cost" in message_lower:
            return "For cost analysis, I recommend using AWS Cost Explorer to review your spending patterns and identify optimization opportunities. Consider Reserved Instances for predictable workloads and Spot Instances for flexible computing needs."
        elif "architecture" in message_lower:
            return "For architecture design, follow AWS Well-Architected principles: operational excellence, security, reliability, performance efficiency, and cost optimization. Start with the AWS Architecture Center for reference architectures."
        else:
            return "I recommend reviewing your AWS setup using the AWS Trusted Advisor and Cost Explorer tools. Focus on right-sizing instances, optimizing storage, and implementing cost monitoring practices."

    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status"""
        return {
            "llm_model": self.model_id,
            "circuit_breaker_state": self.circuit_breaker.state,
            "circuit_breaker_failures": self.circuit_breaker.failure_count,
            "agents_count": len(self.agents),
            "crews_count": len(self.crews),
            "tools_count": len(self._aws_tools),
            "cache_stats": cache_manager.get_stats()
        }

# Initialize global CrewAI manager (singleton pattern)
_crewai_manager = None

def get_crewai_manager():
    """Get or create the global CrewAI manager instance"""
    global _crewai_manager
    if _crewai_manager is None:
        _crewai_manager = AWSCrewManager()
    return _crewai_manager
