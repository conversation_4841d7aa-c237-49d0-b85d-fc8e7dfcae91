"""
Centralized Configuration Management for CrewAI AWS System
"""

from pydantic import BaseSettings, Field
from typing import List, Optional
import os

class SystemConfig(BaseSettings):
    # AWS Configuration
    aws_region: str = Field("ap-south-1", env="AWS_REGION")
    aws_profile: str = Field("default", env="AWS_PROFILE")
    bedrock_model_id: str = Field("apac.amazon.nova-lite-v1:0", env="BEDROCK_MODEL_ID")
    
    # Bedrock Model Fallbacks (in priority order)
    bedrock_fallback_models: List[str] = [
        "apac.amazon.nova-micro-v1:0",
        "anthropic.claude-3-haiku-20240307-v1:0",
        "meta.llama3-8b-instruct-v1:0",
        "mistral.mistral-7b-instruct-v0:2"
    ]
    
    # API Configuration
    api_host: str = Field("0.0.0.0", env="API_HOST")
    api_port: int = Field(8000, env="API_PORT")
    max_concurrent_requests: int = 50
    request_timeout: int = 300
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # CrewAI Configuration
    max_iterations: int = 5
    circuit_breaker_threshold: int = 3
    circuit_breaker_timeout: int = 300
    max_tool_execution_time: int = 30
    
    # Cache Configuration
    cache_ttl_minutes: int = 10
    max_cache_entries: int = 1000
    enable_caching: bool = True
    
    # Session Management
    max_sessions: int = 100
    session_ttl_hours: int = 24
    cleanup_interval_minutes: int = 60
    
    # MCP Configuration
    auto_configure_servers: bool = Field(True, env="AUTO_CONFIGURE_SERVERS")
    mcp_connection_timeout: int = 30
    mcp_max_retries: int = 3
    
    # Rate Limiting
    rate_limit_per_minute: int = 10
    rate_limit_burst: int = 20
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global configuration instance
config = SystemConfig()
