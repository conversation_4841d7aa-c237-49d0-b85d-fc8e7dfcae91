"""
FastAPI Backend for MCP Client with Bedrock Integration - FINAL OPTIMIZED VERSION
Production-ready with comprehensive error handling, connection pooling, and monitoring
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
import asyncio
import json
import logging
from contextlib import asynccontextmanager, AsyncExitStack
import uvicorn
import boto3
from botocore.config import Config
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
import os
from dotenv import load_dotenv
import uuid
import anyio
from datetime import datetime, timedelta

# Import optimized modules
from config import config
from cache_manager import cache_manager, cached
from security import SecurityValidator

load_dotenv()

try:
    from jsonschema import validate, Draft202012Validator, ValidationError
    JSONSCHEMA_AVAILABLE = True
except ImportError:
    JSONSCHEMA_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Enhanced Telemetry Tracking
class TelemetryTracker:
    """Enhanced telemetry tracker for performance monitoring"""

    def __init__(self):
        self.metrics = {
            "total_requests": 0,
            "total_tool_calls": 0,
            "total_input_tokens": 0,
            "total_output_tokens": 0,
            "total_latency_ms": 0,
            "error_count": 0,
            "tool_execution_times": [],
            "conversation_turns": 0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        self.start_time = datetime.now()
        self._lock = asyncio.Lock()

    async def record_conversation_turn(self, usage: Dict, metrics: Dict, tool_count: int = 0):
        """Record metrics from a conversation turn"""
        async with self._lock:
            self.metrics["total_requests"] += 1
            self.metrics["conversation_turns"] += 1
            self.metrics["total_tool_calls"] += tool_count
            self.metrics["total_input_tokens"] += usage.get("inputTokens", 0)
            self.metrics["total_output_tokens"] += usage.get("outputTokens", 0)
            self.metrics["total_latency_ms"] += metrics.get("latencyMs", 0)

    async def record_error(self):
        """Record an error occurrence"""
        async with self._lock:
            self.metrics["error_count"] += 1

    async def record_tool_execution_time(self, execution_time_ms: float):
        """Record tool execution time"""
        async with self._lock:
            self.metrics["tool_execution_times"].append(execution_time_ms)
            # Keep only last 1000 entries to prevent memory growth
            if len(self.metrics["tool_execution_times"]) > 1000:
                self.metrics["tool_execution_times"] = self.metrics["tool_execution_times"][-1000:]

    async def record_cache_hit(self):
        """Record cache hit"""
        async with self._lock:
            self.metrics["cache_hits"] += 1

    async def record_cache_miss(self):
        """Record cache miss"""
        async with self._lock:
            self.metrics["cache_misses"] += 1

    def get_summary(self) -> Dict:
        """Get telemetry summary"""
        uptime_seconds = (datetime.now() - self.start_time).total_seconds()
        avg_latency = (self.metrics["total_latency_ms"] / max(1, self.metrics["total_requests"]))
        avg_tool_time = (sum(self.metrics["tool_execution_times"]) /
                        max(1, len(self.metrics["tool_execution_times"])))
        cache_hit_ratio = (self.metrics["cache_hits"] / 
                          max(1, self.metrics["cache_hits"] + self.metrics["cache_misses"]))

        return {
            **self.metrics,
            "uptime_seconds": uptime_seconds,
            "average_latency_ms": avg_latency,
            "average_tool_execution_ms": avg_tool_time,
            "success_rate": 1 - (self.metrics["error_count"] / max(1, self.metrics["total_requests"])),
            "cache_hit_ratio": cache_hit_ratio,
            "requests_per_second": self.metrics["total_requests"] / max(1, uptime_seconds)
        }

# Global telemetry tracker
telemetry = TelemetryTracker()

# Enhanced Pydantic Models
class MCPServerConfig(BaseModel):
    name: str = Field(..., min_length=1, max_length=50)
    command: str = Field(..., min_length=1)
    args: List[str] = []
    env: Dict[str, str] = {}
    description: str = ""
    enabled: bool = True
    retry_count: int = Field(3, ge=0, le=10)
    timeout_seconds: int = Field(30, ge=1, le=300)

    @validator('name')
    def sanitize_name(cls, v):
        return SecurityValidator.sanitize_input(v)

    @validator('command')
    def validate_command(cls, v):
        # Basic command validation
        if not v or v.strip() != v:
            raise ValueError("Command cannot be empty or have leading/trailing whitespace")
        return v

class ChatMessage(BaseModel):
    role: str = Field(..., regex=r'^(user|assistant|system)$')
    content: str = Field(..., min_length=1, max_length=10000)
    timestamp: Optional[str] = None

    @validator('content')
    def sanitize_content(cls, v):
        return SecurityValidator.sanitize_input(v)

class ChatRequest(BaseModel):
    message: str = Field(..., min_length=1, max_length=10000)
    conversation_id: Optional[str] = Field(None, regex=r'^[a-zA-Z0-9_-]{1,50}$')
    use_tools: bool = True
    priority: Optional[str] = Field("normal", regex=r'^(low|normal|high)$')

    @validator('message')
    def sanitize_message(cls, v):
        return SecurityValidator.sanitize_input(v)

class ChatResponse(BaseModel):
    response: str
    conversation_id: str
    tools_used: List[Dict[str, Any]] = []
    status: str = "success"
    processing_time_ms: Optional[float] = None
    cached: bool = False

# Enhanced MCP Server Connection with Health Monitoring
class MCPServerConnection:
    """Enhanced MCP server connection with health monitoring and auto-recovery"""

    def __init__(self, config: MCPServerConfig):
        self.config = config
        self.status = "disconnected"
        self.tools = {}
        self.resources = {}
        self.error = None
        self.session = None
        self.exit_stack = None
        self.last_health_check = None
        self.connection_attempts = 0
        self.last_connection_attempt = None
        self._lock = asyncio.Lock()

    async def connect(self) -> bool:
        """Connect to the MCP server with retry logic"""
        async with self._lock:
            self.status = "connecting"
            self.error = None
            self.connection_attempts += 1
            self.last_connection_attempt = datetime.now()

            try:
                # Create server parameters with validation
                env = {k: SecurityValidator.sanitize_input(str(v)) for k, v in self.config.env.items()}
                
                server_params = StdioServerParameters(
                    command=self.config.command,
                    args=self.config.args,
                    env=env
                )

                # Create exit stack for cleanup
                self.exit_stack = AsyncExitStack()

                # Connect to server with timeout
                stdio_transport = await asyncio.wait_for(
                    self.exit_stack.enter_async_context(stdio_client(server_params)),
                    timeout=self.config.timeout_seconds
                )

                # Create session
                self.session = await self.exit_stack.enter_async_context(
                    ClientSession(stdio_transport[0], stdio_transport[1])
                )

                # Initialize session with timeout
                await asyncio.wait_for(
                    self.session.initialize(),
                    timeout=self.config.timeout_seconds
                )

                # Get tools and resources
                tools_result = await asyncio.wait_for(
                    self.session.list_tools(),
                    timeout=self.config.timeout_seconds
                )
                self.tools = {tool.name: tool for tool in tools_result.tools}

                try:
                    resources_result = await asyncio.wait_for(
                        self.session.list_resources(),
                        timeout=self.config.timeout_seconds
                    )
                    self.resources = {resource.name: resource for resource in resources_result.resources}
                except Exception as e:
                    logger.warning(f"Could not list resources for {self.config.name}: {e}")
                    self.resources = {}

                self.status = "connected"
                self.last_health_check = datetime.now()
                logger.info(f"✅ Connected to {self.config.name} with {len(self.tools)} tools and {len(self.resources)} resources")
                return True

            except asyncio.TimeoutError:
                self.status = "error"
                self.error = f"Connection timeout after {self.config.timeout_seconds}s"
                logger.error(f"❌ Connection to {self.config.name} timed out")
                await self.cleanup()
                return False
            except Exception as e:
                self.status = "error"
                self.error = SecurityValidator.sanitize_log_message(str(e))
                logger.error(f"❌ Failed to connect to {self.config.name}: {self.error}")
                await self.cleanup()
                return False

    async def health_check(self) -> bool:
        """Perform health check on the connection"""
        if self.status != "connected" or not self.session:
            return False

        try:
            # Simple health check - try to list tools
            await asyncio.wait_for(
                self.session.list_tools(),
                timeout=10
            )
            self.last_health_check = datetime.now()
            return True
        except Exception as e:
            logger.warning(f"Health check failed for {self.config.name}: {e}")
            self.status = "unhealthy"
            return False

    async def auto_reconnect(self) -> bool:
        """Attempt to reconnect if connection is lost"""
        if self.connection_attempts >= self.config.retry_count:
            logger.warning(f"Max reconnection attempts reached for {self.config.name}")
            return False

        # Exponential backoff
        if self.last_connection_attempt:
            wait_time = min(60, 2 ** self.connection_attempts)
            time_since_last = (datetime.now() - self.last_connection_attempt).total_seconds()
            if time_since_last < wait_time:
                return False

        logger.info(f"Attempting to reconnect to {self.config.name} (attempt {self.connection_attempts + 1})")
        return await self.connect()

    async def cleanup(self):
        """Clean up the connection"""
        if self.exit_stack:
            try:
                await self.exit_stack.aclose()
            except Exception as e:
                logger.warning(f"Error during cleanup for {self.config.name}: {e}")
            finally:
                self.exit_stack = None
                self.session = None
                self.status = "disconnected"

    @cached(ttl_minutes=1, key_func=lambda self, tool_name, arguments: f"{self.config.name}_{tool_name}_{hash(json.dumps(arguments, sort_keys=True))}")
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on this server with caching and error handling"""
        start_time = datetime.now()
        
        if self.status != "connected" or not self.session:
            # Try to reconnect
            if not await self.auto_reconnect():
                raise RuntimeError(f"Server {self.config.name} is not connected and reconnection failed")

        if tool_name not in self.tools:
            available_tools = list(self.tools.keys())
            raise ValueError(f"Tool {tool_name} not found on server {self.config.name}. Available tools: {available_tools}")

        try:
            # Sanitize arguments
            sanitized_args = {}
            for k, v in arguments.items():
                if isinstance(v, str):
                    sanitized_args[k] = SecurityValidator.sanitize_input(v)
                else:
                    sanitized_args[k] = v

            # Execute tool with timeout
            result = await asyncio.wait_for(
                self.session.call_tool(tool_name, sanitized_args),
                timeout=self.config.timeout_seconds
            )

            # Record metrics
            execution_time = (datetime.now() - start_time).total_seconds() * 1000
            await telemetry.record_tool_execution_time(execution_time)

            return {
                "success": True,
                "result": result.content,
                "server": self.config.name,
                "tool": tool_name,
                "execution_time_ms": execution_time
            }

        except asyncio.TimeoutError:
            await telemetry.record_error()
            error_msg = f"Tool {tool_name} timed out after {self.config.timeout_seconds}s"
            logger.error(f"⏰ {error_msg} on {self.config.name}")
            return {
                "success": False,
                "error": error_msg,
                "server": self.config.name,
                "tool": tool_name
            }
        except Exception as e:
            await telemetry.record_error()
            error_msg = SecurityValidator.sanitize_log_message(str(e))
            logger.error(f"❌ Error calling tool {tool_name} on {self.config.name}: {error_msg}")
            
            # Mark connection as unhealthy
            self.status = "unhealthy"
            
            return {
                "success": False,
                "error": error_msg,
                "server": self.config.name,
                "tool": tool_name
            }

# Enhanced MCP Client Manager
class MCPClientManager:
    """Enhanced MCP client manager with connection pooling and health monitoring"""

    def __init__(self):
        self.connections: Dict[str, MCPServerConnection] = {}
        self._health_check_task = None
        self._health_check_interval = 60  # 1 minute
        self._lock = asyncio.Lock()
        logger.info("🚀 Enhanced MCP Client Manager initialized")

    async def start_health_monitoring(self):
        """Start background health monitoring"""
        if not self._health_check_task:
            self._health_check_task = asyncio.create_task(self._health_check_worker())
            logger.info("🏥 Health monitoring started")

    async def _health_check_worker(self):
        """Background worker for health monitoring"""
        while True:
            try:
                await asyncio.sleep(self._health_check_interval)
                await self._perform_health_checks()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health check worker error: {e}")

    async def _perform_health_checks(self):
        """Perform health checks on all connections"""
        unhealthy_connections = []
        
        for name, connection in self.connections.items():
            if connection.status == "connected":
                is_healthy = await connection.health_check()
                if not is_healthy:
                    unhealthy_connections.append(name)

        # Attempt to reconnect unhealthy connections
        for name in unhealthy_connections:
            connection = self.connections[name]
            logger.info(f"🔄 Attempting to restore unhealthy connection: {name}")
            await connection.auto_reconnect()

    async def add_server(self, config: MCPServerConfig) -> bool:
        """Add and connect to an MCP server"""
        async with self._lock:
            if config.name in self.connections:
                logger.info(f"♻️ Replacing existing connection to {config.name}")
                await self.remove_server(config.name)

            connection = MCPServerConnection(config)
            self.connections[config.name] = connection
            
            success = await connection.connect()
            if not success:
                logger.error(f"❌ Failed to connect to server {config.name}")
                # Remove failed connection
                del self.connections[config.name]
            
            return success

    async def remove_server(self, server_name: str):
        """Remove and disconnect from an MCP server"""
        async with self._lock:
            if server_name in self.connections:
                await self.connections[server_name].cleanup()
                del self.connections[server_name]
                logger.info(f"🗑️ Removed server {server_name}")

    async def cleanup(self):
        """Clean up all connections and stop health monitoring"""
        # Stop health monitoring
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass

        # Clean up all connections
        cleanup_tasks = []
        for connection in self.connections.values():
            cleanup_tasks.append(connection.cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        self.connections.clear()
        logger.info("🧹 All MCP connections cleaned up")

    def get_available_tools(self) -> Dict[str, Dict[str, Any]]:
        """Get all available tools from all connected servers"""
        tools = {}
        for server_name, connection in self.connections.items():
            if connection.status == "connected":
                for tool_name, tool in connection.tools.items():
                    key = f"{server_name}::{tool_name}"
                    tools[key] = {
                        "server": server_name,
                        "tool": {
                            "name": tool_name,
                            "description": getattr(tool, 'description', ''),
                            "input_schema": getattr(tool, 'inputSchema', {})
                        }
                    }
        return tools

    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on a specific server"""
        if server_name not in self.connections:
            available_servers = list(self.connections.keys())
            raise ValueError(f"Server {server_name} not found. Available servers: {available_servers}")
        
        connection = self.connections[server_name]
        return await connection.call_tool(tool_name, arguments)

    def get_connection_stats(self) -> Dict[str, Any]:
        """Get statistics about all connections"""
        stats = {
            "total_servers": len(self.connections),
            "connected_servers": 0,
            "disconnected_servers": 0,
            "unhealthy_servers": 0,
            "total_tools": 0,
            "total_resources": 0,
            "servers": {}
        }

        for name, connection in self.connections.items():
            server_stats = {
                "status": connection.status,
                "tools_count": len(connection.tools),
                "resources_count": len(connection.resources),
                "connection_attempts": connection.connection_attempts,
                "last_health_check": connection.last_health_check.isoformat() if connection.last_health_check else None,
                "error": connection.error
            }
            
            stats["servers"][name] = server_stats
            stats["total_tools"] += len(connection.tools)
            stats["total_resources"] += len(connection.resources)
            
            if connection.status == "connected":
                stats["connected_servers"] += 1
            elif connection.status == "unhealthy":
                stats["unhealthy_servers"] += 1
            else:
                stats["disconnected_servers"] += 1

        return stats

# Helper function to get correct executable name based on OS
def get_executable_name(base_name: str) -> str:
    """Get the correct executable name based on the operating system"""
    if os.name == 'nt':  # Windows
        return f"{base_name}.exe"
    else:  # Unix-like systems
        return base_name

# Enhanced Default Server Configurations
DEFAULT_MCP_SERVERS = [
    MCPServerConfig(
        name="cost-explorer",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-cost-explorer-mcp-server@latest",
            get_executable_name("awslabs.cost-explorer-mcp-server")
        ],
        env={
            "AWS_PROFILE": config.aws_profile,
            "AWS_REGION": config.aws_region,
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Cost Explorer MCP Server for cost analysis and billing insights",
        enabled=True,
        timeout_seconds=30,
        retry_count=3
    ),
    MCPServerConfig(
        name="cloudformation",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-cfn-mcp-server@latest",
            get_executable_name("awslabs.cfn-mcp-server")
        ],
        env={
            "AWS_PROFILE": config.aws_profile,
            "AWS_REGION": config.aws_region
        },
        description="AWS CloudFormation MCP Server for infrastructure management",
        enabled=True,
        timeout_seconds=45,
        retry_count=3
    ),
    MCPServerConfig(
        name="aws-pricing",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-aws-pricing-mcp-server@latest",
            get_executable_name("awslabs.aws-pricing-mcp-server")
        ],
        env={
            "AWS_PROFILE": config.aws_profile,
            "AWS_REGION": config.aws_region,
            "FASTMCP_LOG_LEVEL": "ERROR"
        },
        description="AWS Pricing MCP Server for real-time service pricing information",
        enabled=True,
        timeout_seconds=20,
        retry_count=3
    ),
    MCPServerConfig(
        name="billing-cost-management",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-billing-cost-management-mcp-server@latest",
            get_executable_name("awslabs.billing-cost-management-mcp-server")
        ],
        env={
            "AWS_PROFILE": config.aws_profile,
            "AWS_REGION": config.aws_region
        },
        description="AWS Billing and Cost Management for optimization recommendations",
        enabled=True,
        timeout_seconds=30,
        retry_count=3
    ),
    MCPServerConfig(
        name="aws-diagram",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-aws-diagram-mcp-server@latest",
            get_executable_name("awslabs.aws-diagram-mcp-server")
        ],
        env={"AWS_REGION": config.aws_region},
        description="AWS architecture diagram generation",
        enabled=True,
        timeout_seconds=60,
        retry_count=2
    ),
    MCPServerConfig(
        name="aws-cdk",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-cdk-mcp-server@latest",
            get_executable_name("awslabs.cdk-mcp-server")
        ],
        env={"AWS_REGION": config.aws_region},
        description="AWS CDK best practices and Well-Architected templates",
        enabled=True,
        timeout_seconds=45,
        retry_count=3
    ),
    MCPServerConfig(
        name="aws-documentation",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-aws-documentation-mcp-server@latest",
            get_executable_name("awslabs.aws-documentation-mcp-server")
        ],
        description="Latest AWS documentation and best practices",
        enabled=True,
        timeout_seconds=30,
        retry_count=3
    ),
    MCPServerConfig(
        name="cloud-control-api",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-ccapi-mcp-server@latest",
            get_executable_name("awslabs.ccapi-mcp-server")
        ],
        env={
            "AWS_PROFILE": config.aws_profile,
            "AWS_REGION": config.aws_region
        },
        description="Comprehensive AWS resource management with 1100+ resource types",
        enabled=True,
        timeout_seconds=40,
        retry_count=3
    ),
    MCPServerConfig(
        name="aws-cloudwatch",
        command="uv",
        args=[
            "tool", "run", "--from",
            "awslabs-cloudwatch-mcp-server@latest",
            get_executable_name("awslabs.cloudwatch-mcp-server")
        ],
        env={
            "AWS_PROFILE": config.aws_profile,
            "AWS_REGION": config.aws_region
        },
        description="CloudWatch monitoring, metrics, and alerting design",
        enabled=True,
        timeout_seconds=25,
        retry_count=3
    )
]

# Export for use in other modules
__all__ = [
    'MCPClientManager',
    'MCPServerConfig', 
    'DEFAULT_MCP_SERVERS',
    'telemetry',
    'TelemetryTracker'
]
