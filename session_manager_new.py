"""
Enhanced Session Manager with optimizations and proper resource management
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import deque

from config import config
from cache_manager import cache_manager
from security import SecurityValidator

logger = logging.getLogger(__name__)

@dataclass
class ConversationTurn:
    """Represents a single conversation turn"""
    user_message: str
    assistant_response: str
    tools_used: List[Dict[str, Any]]
    timestamp: datetime
    processing_time_ms: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            **asdict(self),
            "timestamp": self.timestamp.isoformat()
        }

class EnhancedChatSession:
    """Enhanced chat session with memory management and context retention"""
    
    def __init__(self, session_id: str, max_history: int = 50):
        self.session_id = session_id
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        self.conversation_history: deque = deque(maxlen=max_history)
        self.context_summary = ""
        self.total_tokens_used = 0
        self.total_tools_used = 0
        self._lock = asyncio.Lock()
        
    async def add_turn(
        self, 
        user_message: str, 
        assistant_response: str, 
        tools_used: List[Dict[str, Any]] = None,
        processing_time_ms: float = None
    ):
        """Add a conversation turn with thread safety"""
        async with self._lock:
            # Sanitize inputs
            user_message = SecurityValidator.sanitize_input(user_message)
            assistant_response = SecurityValidator.sanitize_input(assistant_response)
            
            turn = ConversationTurn(
                user_message=user_message,
                assistant_response=assistant_response,
                tools_used=tools_used or [],
                timestamp=datetime.now(),
                processing_time_ms=processing_time_ms
            )
            
            self.conversation_history.append(turn)
            self.last_activity = datetime.now()
            self.total_tools_used += len(tools_used or [])
            
            # Update context summary periodically
            if len(self.conversation_history) % 10 == 0:
                await self._update_context_summary()
    
    async def _update_context_summary(self):
        """Update context summary for long conversations"""
        try:
            # Create a summary of the last 20 turns
            recent_turns = list(self.conversation_history)[-20:]
            if len(recent_turns) >= 10:
                # Simple summarization - in production, use an LLM
                topics = []
                for turn in recent_turns:
                    # Extract key topics from user messages
                    message_lower = turn.user_message.lower()
                    if "cost" in message_lower:
                        topics.append("cost_analysis")
                    elif "architecture" in message_lower:
                        topics.append("architecture_design")
                    elif "optimize" in message_lower:
                        topics.append("optimization")
                
                # Count topic frequency
                topic_counts = {}
                for topic in topics:
                    topic_counts[topic] = topic_counts.get(topic, 0) + 1
                
                # Create summary
                main_topics = sorted(topic_counts.items(), key=lambda x: x[1], reverse=True)[:3]
                self.context_summary = f"Main discussion topics: {', '.join([t[0] for t in main_topics])}"
                
        except Exception as e:
            logger.warning(f"Failed to update context summary for session {self.session_id}: {e}")

    def get_bedrock_messages(self, max_turns: int = 10) -> List[Dict[str, Any]]:
        """Get conversation history formatted for Bedrock"""
        messages = []
        
        # Add context summary if available
        if self.context_summary and len(self.conversation_history) > max_turns:
            messages.append({
                "role": "system",
                "content": [{"text": f"Previous conversation context: {self.context_summary}"}]
            })
        
        # Get recent conversation turns
        recent_turns = list(self.conversation_history)[-max_turns:]
        
        for turn in recent_turns:
            # Add user message
            messages.append({
                "role": "user",
                "content": [{"text": turn.user_message}]
            })
            
            # Add assistant response
            content = [{"text": turn.assistant_response}]
            
            # Add tool usage information
            if turn.tools_used:
                tool_info = f"\n\nTools used: {', '.join([tool.get('tool', 'unknown') for tool in turn.tools_used])}"
                content[0]["text"] += tool_info
            
            messages.append({
                "role": "assistant", 
                "content": content
            })
        
        return messages
    
    def get_context_for_bedrock(self) -> str:
        """Get context string for system message"""
        if not self.conversation_history:
            return "This is a new conversation."
        
        context_parts = []
        
        # Add session info
        context_parts.append(f"Session started: {self.created_at.strftime('%Y-%m-%d %H:%M')}")
        context_parts.append(f"Total conversation turns: {len(self.conversation_history)}")
        
        # Add context summary if available
        if self.context_summary:
            context_parts.append(f"Context: {self.context_summary}")
        
        # Add recent activity summary
        if self.total_tools_used > 0:
            context_parts.append(f"Tools used in session: {self.total_tools_used}")
        
        return " | ".join(context_parts)
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get session statistics"""
        return {
            "session_id": self.session_id,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "total_turns": len(self.conversation_history),
            "total_tools_used": self.total_tools_used,
            "context_summary": self.context_summary,
            "session_duration_minutes": (datetime.now() - self.created_at).total_seconds() / 60
        }

class EnhancedSessionManager:
    """Enhanced session manager with resource management and persistence"""
    
    def __init__(self):
        self.sessions: Dict[str, EnhancedChatSession] = {}
        self.max_sessions = config.max_sessions
        self.session_ttl = timedelta(hours=config.session_ttl_hours)
        self._cleanup_task = None
        self._lock = asyncio.Lock()
        self.backend = None  # For compatibility
        
        logger.info(f"Enhanced Session Manager initialized (max_sessions: {self.max_sessions})")
    
    async def start_background_tasks(self):
        """Start background cleanup tasks"""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            logger.info("Session cleanup task started")
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of expired sessions"""
        while True:
            try:
                await asyncio.sleep(config.cleanup_interval_minutes * 60)
                await self._cleanup_expired_sessions()
            except asyncio.CancelledError:
                logger.info("Session cleanup task cancelled")
                break
            except Exception as e:
                logger.error(f"Session cleanup error: {e}")
    
    async def _cleanup_expired_sessions(self):
        """Clean up expired and excess sessions"""
        async with self._lock:
            current_time = datetime.now()
            expired_sessions = []
            
            # Find expired sessions
            for sid, session in self.sessions.items():
                if current_time - session.last_activity > self.session_ttl:
                    expired_sessions.append(sid)
            
            # Remove expired sessions
            removed_count = 0
            for sid in expired_sessions:
                try:
                    del self.sessions[sid]
                    removed_count += 1
                except KeyError:
                    pass  # Session already removed
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} expired sessions")
            
            # Enforce session limit by removing oldest sessions
            if len(self.sessions) > self.max_sessions:
                sorted_sessions = sorted(
                    self.sessions.items(),
                    key=lambda x: x[1].last_activity
                )
                
                excess_count = len(self.sessions) - self.max_sessions
                for sid, _ in sorted_sessions[:excess_count]:
                    try:
                        del self.sessions[sid]
                        logger.info(f"Removed excess session {sid}")
                    except KeyError:
                        pass
    
    def get_or_create_session(self, session_id: str) -> EnhancedChatSession:
        """Get existing session or create new one"""
        # Validate session ID
        if not SecurityValidator.validate_conversation_id(session_id):
            session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:19]}"
        
        if session_id not in self.sessions:
            # Check if we're at capacity
            if len(self.sessions) >= self.max_sessions:
                # Remove oldest session
                oldest_session = min(
                    self.sessions.items(),
                    key=lambda x: x[1].last_activity
                )
                del self.sessions[oldest_session[0]]
                logger.info(f"Removed oldest session {oldest_session[0]} to make room")
            
            # Create new session
            self.sessions[session_id] = EnhancedChatSession(session_id)
            logger.debug(f"Created new session {session_id}")
        
        # Update last activity
        self.sessions[session_id].last_activity = datetime.now()
        return self.sessions[session_id]
    
    def get_session(self, session_id: str) -> Optional[EnhancedChatSession]:
        """Get existing session"""
        return self.sessions.get(session_id)
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        async with self._lock:
            if session_id in self.sessions:
                del self.sessions[session_id]
                logger.debug(f"Deleted session {session_id}")
                return True
            return False
    
    def get_all_sessions_stats(self) -> Dict[str, Any]:
        """Get statistics for all sessions"""
        total_turns = sum(len(session.conversation_history) for session in self.sessions.values())
        total_tools = sum(session.total_tools_used for session in self.sessions.values())
        
        # Calculate average session age
        if self.sessions:
            avg_age_minutes = sum(
                (datetime.now() - session.created_at).total_seconds() / 60
                for session in self.sessions.values()
            ) / len(self.sessions)
        else:
            avg_age_minutes = 0
        
        return {
            "active_sessions": len(self.sessions),
            "max_sessions": self.max_sessions,
            "session_ttl_hours": self.session_ttl.total_seconds() / 3600,
            "total_conversation_turns": total_turns,
            "total_tools_used": total_tools,
            "average_session_age_minutes": avg_age_minutes,
            "memory_usage_mb": self._estimate_memory_usage()
        }
    
    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage of session data"""
        try:
            import sys
            total_size = 0
            
            for session in self.sessions.values():
                # Rough estimation
                total_size += sys.getsizeof(session)
                total_size += sum(sys.getsizeof(turn) for turn in session.conversation_history)
            
            return total_size / (1024 * 1024)  # Convert to MB
        except Exception:
            return 0.0
    
    async def cleanup(self):
        """Cleanup resources"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        self.sessions.clear()
        logger.info("Session manager cleaned up")

# Global session manager instance
session_manager = EnhancedSessionManager()
